use pyo3::prelude::*;
use numpy::{PyReadonlyArray1, PyR<PERSON>onlyArray2, PyArray1};
use ndarray::{<PERSON>rray1, <PERSON>rray2, <PERSON><PERSON>yView1, <PERSON><PERSON>yView2};

const MIN_FLOAT: f64 = 1e-15;

/// Portfolio variance calculation using matrix multiplication
#[pyfunction]
fn portfolio_variance_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    cov_matrix: PyReadonlyArray2<f64>,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let cov_matrix = cov_matrix.as_array();
    
    // Calculate weights^T * cov_matrix * weights
    let temp = cov_matrix.dot(&weights);
    let variance = weights.dot(&temp);
    
    Ok(variance)
}

/// Downside standard deviation calculation for negative returns
#[pyfunction]
fn downside_std_rust(_py: Python, arr: PyReadonlyArray1<f64>) -> PyResult<f64> {
    let arr = arr.as_array();
    let mut sum = 0.0;
    let mut count = 0;
    
    for &x in arr.iter() {
        if x.is_finite() && x < 0.0 {
            sum += x * x;
            count += 1;
        }
    }
    
    if count > 0 {
        Ok((sum / count as f64).sqrt())
    } else {
        Ok(0.0)
    }
}

/// Negative Sharpe ratio calculation for optimization
#[pyfunction]
fn neg_sharpe_ratio_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    mean_returns: PyReadonlyArray1<f64>,
    cov_matrix: PyReadonlyArray2<f64>,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let mean_returns = mean_returns.as_array();
    let cov_matrix = cov_matrix.as_array();
    
    // Calculate portfolio return
    let mut port_return = 0.0;
    for (i, &w) in weights.iter().enumerate() {
        let w = if w.is_finite() { w } else { 0.0 };
        let mr = if mean_returns[i].is_finite() { mean_returns[i] } else { 0.0 };
        port_return += w * mr;
    }
    
    // Calculate portfolio variance
    let temp = cov_matrix.dot(&weights);
    let port_var = weights.dot(&temp);
    let port_var = if port_var.is_finite() { port_var } else { 0.0 };
    let port_vol = port_var.sqrt();
    let port_vol = if port_vol.is_finite() { port_vol } else { 0.0 };
    
    if port_vol > 0.0 {
        Ok(-port_return / port_vol)
    } else {
        Ok(0.0)
    }
}

/// Negative Sortino ratio calculation
#[pyfunction]
fn neg_sortino_ratio_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    mean_returns: PyReadonlyArray1<f64>,
    _cov_matrix: PyReadonlyArray2<f64>,
    returns_array: PyReadonlyArray1<f64>,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let mean_returns = mean_returns.as_array();
    let returns_array = returns_array.as_array();

    // Calculate portfolio return
    let mut port_return = 0.0;
    for (i, &w) in weights.iter().enumerate() {
        port_return += w * mean_returns[i];
    }

    // Clean the returns array: replace NaNs with 0
    let mut clean_returns = Vec::new();
    for &val in returns_array.iter() {
        if val.is_finite() {
            clean_returns.push(val);
        } else {
            clean_returns.push(0.0);
        }
    }

    // Compute downside standard deviation on negative returns
    let mut sum = 0.0;
    let mut count = 0;
    for &x in clean_returns.iter() {
        if x < 0.0 {
            sum += x * x;
            count += 1;
        }
    }

    let dd = if count > 0 {
        (sum / count as f64).sqrt()
    } else {
        0.0
    };

    if dd < MIN_FLOAT {
        Ok(f64::INFINITY)
    } else {
        Ok(-port_return / dd)
    }
}

/// Omega ratio calculation
#[pyfunction]
fn compute_omega_ratio_rust(
    _py: Python,
    arr: PyReadonlyArray1<f64>,
    threshold: f64,
) -> PyResult<f64> {
    let arr = arr.as_array();
    let mut gain_sum = 0.0;
    let mut loss_sum = 0.0;

    for &x in arr.iter() {
        if x.is_finite() {
            if x > threshold {
                gain_sum += x - threshold;
            } else if x < threshold {
                loss_sum += threshold - x;
            }
        }
    }

    if loss_sum < MIN_FLOAT {
        Ok(f64::INFINITY)
    } else {
        Ok(gain_sum / loss_sum)
    }
}

/// Negative Calmar ratio calculation
#[pyfunction]
fn neg_calmar_ratio_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    mean_returns: PyReadonlyArray1<f64>,
    adjusted: PyReadonlyArray2<f64>,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let mean_returns = mean_returns.as_array();
    let adjusted = adjusted.as_array();

    // Calculate portfolio return (for consistency, though not used in final calculation)
    let mut _port_return = 0.0;
    for (i, &w) in weights.iter().enumerate() {
        _port_return += w * mean_returns[i];
    }

    // Compute candidate series as dot product of each row of adjusted and weights
    let t_len = adjusted.nrows();
    let mut candidate_series = vec![0.0; t_len];

    for t in 0..t_len {
        let mut s = 0.0;
        for j in 0..weights.len() {
            let temp = adjusted[[t, j]] * weights[j];
            let temp = if temp.is_finite() { temp } else { 0.0 };
            s += temp;
        }
        candidate_series[t] = s;
    }

    // Compute cumulative returns with safeguard
    let mut cum_returns = vec![0.0; t_len];
    cum_returns[0] = if candidate_series[0].is_finite() { candidate_series[0] } else { 0.0 };

    for t in 1..t_len {
        let val = if candidate_series[t].is_finite() { candidate_series[t] } else { 0.0 };
        cum_returns[t] = cum_returns[t - 1] + val;
    }

    // Compute running peak (max cumulative return so far)
    let mut peak = vec![0.0; t_len];
    peak[0] = cum_returns[0];

    for t in 1..t_len {
        let current = cum_returns[t];
        let prev_peak = peak[t - 1];

        if !current.is_finite() || !prev_peak.is_finite() {
            peak[t] = prev_peak;
        } else if current > prev_peak {
            peak[t] = current;
        } else {
            peak[t] = prev_peak;
        }
    }

    // Calculate maximum drawdown safely
    let mut max_drawdown = 0.0;
    for t in 0..t_len {
        let d = peak[t] - cum_returns[t];
        let d = if d.is_finite() { d } else { 0.0 };
        if d > max_drawdown {
            max_drawdown = d;
        }
    }

    let calmar = if max_drawdown > MIN_FLOAT {
        cum_returns[t_len - 1] / max_drawdown
    } else {
        f64::INFINITY
    };

    Ok(-calmar)
}

/// Compute Calmar ratio for a return series
#[pyfunction]
fn compute_calmar_ratio_rust(
    _py: Python,
    arr: PyReadonlyArray1<f64>,
) -> PyResult<f64> {
    let arr = arr.as_array();
    let n = arr.len();

    // Initialize cumulative returns with safeguard
    let mut cum_returns = vec![0.0; n];
    cum_returns[0] = if arr[0].is_finite() { arr[0] } else { 0.0 };

    for i in 1..n {
        let val = if arr[i].is_finite() { arr[i] } else { 0.0 };
        cum_returns[i] = cum_returns[i - 1] + val;
    }

    // Compute running peak
    let mut peak = vec![0.0; n];
    peak[0] = cum_returns[0];

    for i in 1..n {
        if !cum_returns[i].is_finite() || !peak[i - 1].is_finite() {
            peak[i] = peak[i - 1];
        } else {
            peak[i] = if cum_returns[i] > peak[i - 1] { cum_returns[i] } else { peak[i - 1] };
        }
    }

    // Calculate maximum drawdown
    let mut max_drawdown = 0.0;
    for i in 0..n {
        let d = peak[i] - cum_returns[i];
        let d = if d.is_finite() { d } else { 0.0 };
        if d > max_drawdown {
            max_drawdown = d;
        }
    }

    let final_return = cum_returns[n - 1];
    if max_drawdown > MIN_FLOAT {
        Ok(final_return / max_drawdown)
    } else {
        Ok(f64::INFINITY)
    }
}

/// VaR and CVaR calculation
#[pyfunction]
fn calculate_var_cvar_rust(
    _py: Python,
    returns_array: PyReadonlyArray1<f64>,
    confidence_level: f64,
) -> PyResult<(f64, f64)> {
    let returns_array = returns_array.as_array();

    // Clean the input array (remove NaNs)
    let mut valid_returns = Vec::new();
    for &x in returns_array.iter() {
        if x.is_finite() {
            valid_returns.push(x);
        }
    }

    // If no valid returns, return zeros
    if valid_returns.is_empty() {
        return Ok((0.0, 0.0));
    }

    // Sort returns from worst to best
    valid_returns.sort_by(|a, b| a.partial_cmp(b).unwrap());

    // Find the index at the specified confidence level
    let mut index = ((1.0 - confidence_level) * valid_returns.len() as f64) as usize;
    if index >= valid_returns.len() {
        index = valid_returns.len() - 1;
    }

    // VaR is the loss at this confidence level (negative of return)
    let var = -valid_returns[index];

    // CVaR is the average loss beyond VaR (including the VaR point)
    let tail_index = index + 1;
    let cvar = if tail_index > 0 && tail_index <= valid_returns.len() {
        let tail_sum: f64 = valid_returns[0..tail_index].iter().sum();
        -tail_sum / tail_index as f64
    } else {
        var // Fallback if index is 0 or out of bounds
    };

    Ok((var, cvar))
}

/// Martin ratio calculation (Return / Ulcer Index)
#[pyfunction]
fn compute_martin_ratio_rust(
    _py: Python,
    portfolio_returns: PyReadonlyArray1<f64>,
    risk_free_rate: f64,
) -> PyResult<f64> {
    let portfolio_returns = portfolio_returns.as_array();
    let len = portfolio_returns.len();

    // Calculate cumulative returns
    let mut cum_returns = vec![0.0; len];
    cum_returns[0] = if portfolio_returns[0].is_finite() { portfolio_returns[0] } else { 0.0 };

    for i in 1..len {
        let val = if portfolio_returns[i].is_finite() { portfolio_returns[i] } else { cum_returns[i-1] };
        cum_returns[i] = cum_returns[i-1] + (val - cum_returns[i-1]);
    }

    // Calculate running peak (max cumulative return so far)
    let mut peak = vec![0.0; len];
    peak[0] = cum_returns[0];
    for i in 1..len {
        peak[i] = peak[i-1].max(cum_returns[i]);
    }

    // Calculate drawdowns and squared drawdowns
    let mut squared_dd_sum = 0.0;
    let mut count = 0;
    for i in 0..len {
        if cum_returns[i].is_finite() && peak[i].is_finite() {
            let drawdown = (cum_returns[i] - peak[i]) / (peak[i] + MIN_FLOAT);
            squared_dd_sum += drawdown * drawdown;
            count += 1;
        }
    }

    // Calculate Ulcer Index
    let ulcer_index = if count > 0 {
        (squared_dd_sum / count as f64).sqrt()
    } else {
        0.0
    };

    // Calculate annualized return (assuming daily data with 252 trading days)
    let annualized_return = cum_returns[len - 1] / len as f64 * 252.0;

    // Calculate Martin Ratio
    if ulcer_index > 0.0 {
        Ok((annualized_return - risk_free_rate) / ulcer_index)
    } else if annualized_return <= risk_free_rate {
        Ok(0.0)
    } else {
        Ok(f64::INFINITY)
    }
}

/// Negative modified Sharpe ratio calculation with skewness and kurtosis adjustment
#[pyfunction]
fn neg_modified_sharpe_ratio_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    mean_returns: PyReadonlyArray1<f64>,
    cov_matrix: PyReadonlyArray2<f64>,
    returns_array: PyReadonlyArray2<f64>,
    risk_free_rate: f64,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let mean_returns = mean_returns.as_array();
    let cov_matrix = cov_matrix.as_array();
    let returns_array = returns_array.as_array();

    let n = weights.len();

    // Compute portfolio expected return using mean returns
    let mut port_return = 0.0;
    for i in 0..n {
        port_return += weights[i] * mean_returns[i];
    }

    // Compute portfolio variance
    let temp = cov_matrix.dot(&weights);
    let port_var = weights.dot(&temp);
    let port_vol = port_var.sqrt();

    if port_vol <= MIN_FLOAT {
        return Ok(0.0);
    }

    // Compute portfolio return series from returns_array
    let t_len = returns_array.nrows();
    let mut port_series = vec![0.0; t_len];

    for t in 0..t_len {
        let mut s = 0.0;
        for j in 0..n {
            s += returns_array[[t, j]] * weights[j];
        }
        port_series[t] = s;
    }

    // Calculate sample mean and standard deviation of the series
    let series_mean: f64 = port_series.iter().sum::<f64>() / t_len as f64;

    let series_std = {
        let variance: f64 = port_series.iter()
            .map(|&x| (x - series_mean).powi(2))
            .sum::<f64>() / t_len as f64;
        variance.sqrt()
    };

    // Compute skewness and kurtosis
    let (skew, kurt) = if series_std > MIN_FLOAT {
        let skew: f64 = port_series.iter()
            .map(|&x| ((x - series_mean) / series_std).powi(3))
            .sum::<f64>() / t_len as f64;

        let kurt: f64 = port_series.iter()
            .map(|&x| ((x - series_mean) / series_std).powi(4))
            .sum::<f64>() / t_len as f64;

        (skew, kurt)
    } else {
        (0.0, 3.0) // Normal distribution assumption
    };

    // Use the 95% confidence level: standard normal quantile
    let z = 1.****************;

    // Cornish–Fisher expansion adjustment
    let z_mod = z + (1.0/6.0) * (z*z - 1.0) * skew +
                (1.0/24.0) * (z.powi(3) - 3.0*z) * (kurt - 3.0) -
                (1.0/36.0) * (2.0*z.powi(3) - 5.0*z) * (skew*skew);

    // Adjust volatility: scaling the normal volatility to account for skew/kurtosis
    let mod_vol = port_vol * (z_mod / z);

    // Modified Sharpe ratio (for minimization, return negative value)
    let sharpe_mod = (port_return - risk_free_rate) / mod_vol;
    Ok(-sharpe_mod)
}

/// Cross product calculation for convex hull algorithm
#[pyfunction]
fn cross_product_rust(
    _py: Python,
    o: (f64, f64),
    a: (f64, f64),
    b: (f64, f64)
) -> PyResult<f64> {
    Ok((a.0 - o.0) * (b.1 - o.1) - (a.1 - o.1) * (b.0 - o.0))
}

/// Efficient frontier calculation using Pareto optimality
#[pyfunction]
fn convex_hull_upper_rust(
    _py: Python,
    points: Vec<(f64, f64)>
) -> PyResult<Vec<(f64, f64)>> {
    if points.len() < 2 {
        return Ok(points);
    }

    // Sort by risk (x-coordinate) ascending
    let mut sorted_points = points;
    sorted_points.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());

    // Build Pareto frontier: for each risk level, keep only the highest return
    let mut efficient = Vec::new();
    let mut max_return_so_far = f64::NEG_INFINITY;

    for (risk, return_val) in sorted_points {
        if return_val > max_return_so_far {
            efficient.push((risk, return_val));
            max_return_so_far = return_val;
        }
    }

    Ok(efficient)
}

/// Python module definition
#[pymodule]
fn ratio_calcs_rust(m: &Bound<'_, PyModule>) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(portfolio_variance_rust, m)?)?;
    m.add_function(wrap_pyfunction!(downside_std_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_sharpe_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_sortino_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(compute_omega_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_calmar_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(compute_calmar_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(calculate_var_cvar_rust, m)?)?;
    m.add_function(wrap_pyfunction!(compute_martin_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_modified_sharpe_ratio_rust, m)?)?;

    // Efficient frontier functions
    m.add_function(wrap_pyfunction!(convex_hull_upper_rust, m)?)?;
    m.add_function(wrap_pyfunction!(cross_product_rust, m)?)?;

    Ok(())
}
