{"timestamp": "2025-07-03T23:41:27.772047", "rust_available": true, "test_results": {}, "performance_metrics": {}, "accuracy_validation": {"standard_100": {"rust_points": 5, "python_points": 5, "jaccard_similarity": 0.0, "identical_results": false}, "cvar_100": {"rust_points": 3, "python_points": 6, "jaccard_similarity": 0.0, "identical_results": false}, "standard_500": {"rust_points": 5, "python_points": 8, "jaccard_similarity": 0.0, "identical_results": false}, "cvar_500": {"rust_points": 7, "python_points": 7, "jaccard_similarity": 0.0, "identical_results": false}, "standard_1000": {"rust_points": 7, "python_points": 10, "jaccard_similarity": 0.0, "identical_results": false}, "cvar_1000": {"rust_points": 6, "python_points": 5, "jaccard_similarity": 0.0, "identical_results": false}, "standard_2000": {"rust_points": 8, "python_points": 8, "jaccard_similarity": 0.0, "identical_results": false}, "cvar_2000": {"rust_points": 7, "python_points": 6, "jaccard_similarity": 0.0, "identical_results": false}}, "scaling_analysis": {"conservative": {"500": {"rust_time": 0.0002218000590801239, "python_time": 0.00047619990073144436, "rust_cvar_time": 0.00011890009045600891, "python_cvar_time": 0.00044029997661709785, "speedup": 2.146978241152858, "cvar_speedup": 3.703108844816242, "rust_hull_points": 5, "python_hull_points": 6, "candidates_per_second": 2254282.537496431}, "1000": {"rust_time": 0.0004691001959145069, "python_time": 0.0009611998684704304, "rust_cvar_time": 0.00027629989199340343, "python_cvar_time": 0.000923600047826767, "speedup": 2.049028921415348, "cvar_speedup": 3.3427448746480786, "rust_hull_points": 7, "python_hull_points": 8, "candidates_per_second": 2131740.7426157827}, "2000": {"rust_time": 0.0008064999710768461, "python_time": 0.0021599000319838524, "rust_cvar_time": 0.0005187999922782183, "python_cvar_time": 0.0031675000209361315, "speedup": 2.678115448782886, "cvar_speedup": 6.105435751890852, "rust_hull_points": 8, "python_hull_points": 9, "candidates_per_second": 2479851.297861278}, "5000": {"rust_time": 0.0021407001186162233, "python_time": 0.0054332001600414515, "rust_cvar_time": 0.0014525000005960464, "python_cvar_time": 0.00655149994418025, "speedup": 2.5380482360852783, "cvar_speedup": 4.5104990991337575, "rust_hull_points": 9, "python_hull_points": 8, "candidates_per_second": 2335684.4597327653}, "10000": {"rust_time": 0.0077997001353651285, "python_time": 0.05011780001223087, "rust_cvar_time": 0.0030990999657660723, "python_cvar_time": 0.012787500163540244, "speedup": 6.425606003106259, "cvar_speedup": 4.126198026780746, "rust_hull_points": 10, "python_hull_points": 12, "candidates_per_second": 1282100.5713615}, "20000": {"rust_time": 0.009842999977990985, "python_time": 0.027976599987596273, "rust_cvar_time": 0.007160099921748042, "python_cvar_time": 0.02966020000167191, "speedup": 2.8422838616430095, "cvar_speedup": 4.1424282238830505, "rust_hull_points": 11, "python_hull_points": 13, "candidates_per_second": 2031900.8477821941}}, "balanced": {"500": {"rust_time": 0.00023879995569586754, "python_time": 0.0008441000245511532, "rust_cvar_time": 0.00011559994891285896, "python_cvar_time": 0.0009959000162780285, "speedup": 3.5347578775357387, "cvar_speedup": 8.615055851181676, "rust_hull_points": 5, "python_hull_points": 8, "candidates_per_second": 2093802.7335180638}, "1000": {"rust_time": 0.0004773000255227089, "python_time": 0.00110479979775846, "rust_cvar_time": 0.0002592001110315323, "python_cvar_time": 0.00106120016425848, "speedup": 2.3146862323096524, "cvar_speedup": 4.094134682409077, "rust_hull_points": 7, "python_hull_points": 10, "candidates_per_second": 2095118.262155681}, "2000": {"rust_time": 0.0009588000830262899, "python_time": 0.002230500103905797, "rust_cvar_time": 0.0005905998405069113, "python_cvar_time": 0.0022679001558572054, "speedup": 2.326345338712948, "cvar_speedup": 3.839994528123592, "rust_hull_points": 8, "python_hull_points": 8, "candidates_per_second": 2085940.5786525789}, "5000": {"rust_time": 0.0023227999918162823, "python_time": 0.006059299921616912, "rust_cvar_time": 0.001604699995368719, "python_cvar_time": 0.006895599886775017, "speedup": 2.6086188836598554, "cvar_speedup": 4.297127130726129, "rust_hull_points": 10, "python_hull_points": 8, "candidates_per_second": 2152574.486660953}, "10000": {"rust_time": 0.006160899996757507, "python_time": 0.012942800065502524, "rust_cvar_time": 0.0029790999833494425, "python_cvar_time": 0.013153900159522891, "speedup": 2.1007969732205267, "cvar_speedup": 4.4153939891382175, "rust_hull_points": 11, "python_hull_points": 11, "candidates_per_second": 1623139.4772294662}, "20000": {"rust_time": 0.00939609995111823, "python_time": 0.027287700213491917, "rust_cvar_time": 0.006873500067740679, "python_cvar_time": 0.027466199826449156, "speedup": 2.9041517603529114, "cvar_speedup": 3.9959554165651303, "rust_hull_points": 12, "python_hull_points": 13, "candidates_per_second": 2128542.7043184866}}, "aggressive": {"500": {"rust_time": 0.00023370003327727318, "python_time": 0.0007356000132858753, "rust_cvar_time": 0.00011599995195865631, "python_cvar_time": 0.0009769999887794256, "speedup": 3.1476247691121353, "cvar_speedup": 8.422417184514348, "rust_hull_points": 5, "python_hull_points": 6, "candidates_per_second": 2139494.7745119724}, "1000": {"rust_time": 0.0004688999615609646, "python_time": 0.001121200155466795, "rust_cvar_time": 0.00024969992227852345, "python_cvar_time": 0.0009713999461382627, "speedup": 2.391128699892249, "cvar_speedup": 3.890269317163549, "rust_hull_points": 8, "python_hull_points": 8, "candidates_per_second": 2132651.059878545}, "2000": {"rust_time": 0.0010526999831199646, "python_time": 0.0024206999223679304, "rust_cvar_time": 0.0007728999480605125, "python_cvar_time": 0.0021424000151455402, "speedup": 2.2995154946174914, "cvar_speedup": 2.77189825218853, "rust_hull_points": 8, "python_hull_points": 8, "candidates_per_second": 1899876.5384914821}, "5000": {"rust_time": 0.0022050999104976654, "python_time": 0.006393900141119957, "rust_cvar_time": 0.0015496001578867435, "python_cvar_time": 0.005692799808457494, "speedup": 2.8995965718745724, "cvar_speedup": 3.673721752984983, "rust_hull_points": 9, "python_hull_points": 8, "candidates_per_second": 2267470.9550333065}, "10000": {"rust_time": 0.0041062000673264265, "python_time": 0.011683800024911761, "rust_cvar_time": 0.0030598000157624483, "python_cvar_time": 0.012105799978598952, "speedup": 2.8454044696655902, "cvar_speedup": 3.956402351864947, "rust_hull_points": 10, "python_hull_points": 12, "candidates_per_second": 2435341.6385069285}, "20000": {"rust_time": 0.009363299934193492, "python_time": 0.02545990003272891, "rust_cvar_time": 0.006720000179484487, "python_cvar_time": 0.026145400013774633, "speedup": 2.7191161461946587, "cvar_speedup": 3.8906844219430265, "rust_hull_points": 11, "python_hull_points": 13, "candidates_per_second": 2135999.075172497}}}, "rust_status": {"rust_available": true, "functions_available": ["convex_hull_upper_rust", "cross_product_rust", "efficient_frontier_upper_hull_rust", "efficient_frontier_upper_hull_cvar_rust"], "performance_improvement": "Expected 10-20x speedup for convex hull operations"}, "memory_efficiency": {"1000": {"baseline_mb": 118.390625, "after_generation_mb": 118.390625, "after_rust_mb": 118.390625, "after_python_mb": 118.390625, "generation_overhead_mb": 0.0, "rust_overhead_mb": 0.0, "python_overhead_mb": 0.0}, "5000": {"baseline_mb": 118.390625, "after_generation_mb": 118.41015625, "after_rust_mb": 118.41015625, "after_python_mb": 118.41015625, "generation_overhead_mb": 0.01953125, "rust_overhead_mb": 0.0, "python_overhead_mb": 0.0}, "10000": {"baseline_mb": 118.41015625, "after_generation_mb": 118.41015625, "after_rust_mb": 118.41015625, "after_python_mb": 118.41015625, "generation_overhead_mb": 0.0, "rust_overhead_mb": 0.0, "python_overhead_mb": 0.0}, "20000": {"baseline_mb": 118.41015625, "after_generation_mb": 124.3515625, "after_rust_mb": 124.40625, "after_python_mb": 124.40625, "generation_overhead_mb": 5.94140625, "rust_overhead_mb": 0.0546875, "python_overhead_mb": 0.0}}, "real_world_scenarios": {"user_increased_params": {"config": {"description": "User's increased computational parameters", "max_combos": 100000, "candidates_per_category": 2000, "composite_portfolios": 200, "test_size": 12000}, "total_rust_time": 0.004987199557945132, "total_python_time": 0.015213900245726109, "speedup": 3.0505898288125985, "groups_processed": 6, "total_hull_points": 59, "avg_time_per_group_rust": 0.0008311999263241887, "avg_time_per_group_python": 0.0025356500409543514, "throughput_candidates_per_sec": 2406159.982285598}, "original_params": {"config": {"description": "Original MPT parameters", "max_combos": 20000, "candidates_per_category": 200, "composite_portfolios": 20, "test_size": 1200}, "total_rust_time": 0.00047340034507215023, "total_python_time": 0.0011099998373538256, "speedup": 2.344738124735951, "groups_processed": 6, "total_hull_points": 34, "avg_time_per_group_rust": 7.890005751202504e-05, "avg_time_per_group_python": 0.00018499997289230427, "throughput_candidates_per_sec": 2534852.3981686365}, "extreme_load": {"config": {"description": "Extreme computational load test", "max_combos": 200000, "candidates_per_category": 5000, "composite_portfolios": 500, "test_size": 30000}, "total_rust_time": 0.014959100168198347, "total_python_time": 0.04073949973098934, "speedup": 2.7233924014759734, "groups_processed": 6, "total_hull_points": 66, "avg_time_per_group_rust": 0.002493183361366391, "avg_time_per_group_python": 0.006789916621831556, "throughput_candidates_per_sec": 2005468.2208611188}}}